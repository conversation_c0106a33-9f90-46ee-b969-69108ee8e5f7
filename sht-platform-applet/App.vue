<script>
import store from './store';
import { loginExpire, appVersion, loginSwitch } from './http/api';
import { checkVersion } from './static/utils/updateVersion';

export default {
  onLaunch: async function () {
    console.log('App Launch');

    //#ifdef APP-PLUS
      this.checkAppUpdate();
    //#endif

    //#ifdef MP
    const hideTabbarIndexs = [1, 2];
    hideTabbarIndexs.forEach(index => {
      uni.setTabBarItem({
        index,
        visible: true
      });
    });
    //#endif
  },
  onShow: function () {
    console.log('App Show');
    // #ifdef MP
    checkVersion();
    // #endif

    // store.state.token && this.checkLoginExpire()
  },
  onHide: function () {
    console.log('App Hide');
  },
  methods: {
    async checkLoginExpire() {
      const { data } = await loginExpire();

      if (!data.isExpire) {
        store.dispatch('login_out');
      }
    },
    checkAppUpdate() {
      const platform = uni.getSystemInfoSync().platform;
      // 获取本地应用资源版本号
      plus.runtime.getProperty(plus.runtime.appid, inf => {
        //获取服务器的版本号
        appVersion({ abbr: 'shtop', version: inf.version, fileType: platform === 'android' ? 10 : 20 }).then(({ data }) => {
          if (!data) return;
          const { downloadURL, support, newest, buildUpdateDescription } = data;

          const convertData = {
            describe: buildUpdateDescription, // 版本更新内容 支持<br>自动换行
            edition_url: platform === 'android' ? downloadURL : 'itms-apps://itunes.apple.com/cn/app/1617036314', //apk下载地址 或者 跳转appstore
            edition_force: Number(!support), //是否强制更新 0代表否 1代表是
            package_type: 0, //0是整包升级（apk或者appstore或者安卓应用市场） 1是wgt升级
            edition_issue: 1 //是否发行  0否 1是 为了控制上架应用市场审核时不能弹出热更新框
          };
          //判断后台返回是否最新版
          if (newest === 0) {
            //跳转更新页面 （注意！！！如果pages.json第一页的代码里有一打开就跳转其他页面的操作，下面这行代码最好写在setTimeout里面设置延时3到5秒再执行）
            setTimeout(() => {
              uni.navigateTo({
                url: '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' +  encodeURIComponent(JSON.stringify(convertData))
              });
            }, 3000);
          }
        });
      });
    },
  }
};
</script>

<style lang="scss">
@import 'uview-ui/index.scss';
@import './static/css/common.scss';
</style>
