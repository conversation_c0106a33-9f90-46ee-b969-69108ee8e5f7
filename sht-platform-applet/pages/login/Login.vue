<template>
  <view id="login">
    <header>
      <!-- 占位 -->
      <u-navbar :is-back="false" :border-bottom="false" />
      <text>你好, 请登录</text>
    </header>
    <main>
      <u-form :model="user" ref="uForm" label-position="top">
        <u-form-item label="账号" prop="loginUserNo">
          <u-input
            v-model="user.loginUserNo"
            :clearable="false"
            placeholder="请输入代理商编号/登录手机号"
          />
          <u-icon
            class="select-down"
            v-show="isShowArrowDown"
            slot="right"
            name="arrow-down-fill"
            size="34"
            @click="showAccounts = true"
          />
        </u-form-item>
        <u-form-item label="密码" prop="loginPwd">
          <u-input
            v-model="user.loginPwd"
            type="password"
            placeholder="请输入密码"
            password-icon
            :clearable="false"
          />
        </u-form-item>
      </u-form>

      <view class="rember-psw">
        <u-checkbox v-model="remember" shape="circle">记住密码</u-checkbox>
        <!-- <text @click="register">没有账号? <text>注册</text></text> -->
      </view>

      <view style="margin: 100rpx 0rpx 30rpx">
        <u-button
          type="primary"
          ripple
          shape="circle"
          @click="submit"
          :disabled="!(user.loginUserNo && user.loginPwd)"
          >登 录</u-button
        >
      </view>
      <view class="agreement">
        <u-checkbox v-model="agree" />
        <p
          >我已阅读并同意<span @click="toWebview(0)">《服务协议》</span>和<span
            @click="toWebview(1)"
            >《隐私规则》</span
          ></p
        >
      </view>
    </main>

    <view class="logo">
      <image src="../../static/images/common/aboutUs.png" mode="widthFix" />
      <text>- 本产品由商互通开放平台提供技术支持 -</text>
    </view>

    <!-- 账号列表 -->
    <u-popup
      mode="center"
      v-model="showAccounts"
      border-radius="20"
      width="80%"
    >
      <view class="proup-title">
        <text>选择账号登录</text>
        <u-icon
          name="close"
          :size="32"
          color="#888"
          @click="showAccounts = false"
        />
      </view>

      <scroll-view style="height: 25vh" scroll-y>
        <u-cell-group :border="false">
          <u-cell-item
            v-for="(a, index) in accounts"
            :key="index"
            :title="a.loginUserNo"
            :arrow="false"
            @click="selectAccount(a)"
          >
            <view slot="right-icon" @click.stop="deleteAccount(index)">
              <u-icon name="trash" size="36" color="#888" />
            </view>
          </u-cell-item>
        </u-cell-group>
      </scroll-view>
    </u-popup>
  </view>
</template>

<script>
  import { login, getChannel } from '../../http/api';

  const rules = {
    loginUserNo: [{ required: true, message: '账号不能为空', trigger: 'blur' }],
    loginPwd: [
      { required: true, min: 6, message: '密码必须6位及以上', trigger: 'blur' }
    ]
  };

  export default {
    name: 'Login',
    data() {
      return {
        remember: false,
        accounts: [], // remember account list
        showAccounts: false,
        agree: false,
        user: {
          loginUserNo: '',
          loginPwd: ''
        }
      };
    },
    computed: {
      isShowArrowDown() {
        return !!this.accounts.length;
      }
    },
    onLoad() {
      const accounts = uni.getStorageSync('accounts') || [];
      this.accounts = accounts;

      const last_login_account = uni.getStorageSync('last_login_account') || '';

      if (last_login_account) {
        const item = accounts.find((a) => a.loginUserNo === last_login_account);
        this.user = item
          ? Object.assign(this.user, item)
          : { loginUserNo: last_login_account, loginPwd: '' };
      }

      this.agree = uni.getStorageSync('agreement') || false;
    },
    onReady() {
      this.$refs.uForm.setRules(rules);
    },
    methods: {
      selectAccount(val) {
        this.user = Object.assign(this.user, val);
        this.showAccounts = false;
      },

      deleteAccount(index) {
        this.accounts.splice(index, 1);
        if (!this.accounts.length) {
          this.showAccounts = false;
        }
      },

      submit() {
        if (!this.agree) return this.$u.toast('请阅读并同意用户协议及隐私规则');
        this.$refs.uForm.validate(async (valid) => {
          if (valid) {
            //#ifdef APP-PLUS
            await this.checkIosIsPass();
            //#endif

            login(this.user).then((res) => {
              if (res.code == '00') {
                const {
                  agentCode,
                  realName,
                  accountName,
                  agentLevel,
                  mobile,
                  payMarketMode,
                  companyName,
                  appAuthStatus,
                  payTaxType,
                  taxationRegisterStatus,
                  idCardNo,
                  superAgentCode,
                  showCreditDiscountOpenConf,
                  showSecTransOpenConf,
                  memberLevel,
                  isMobileVerify,
                  loginName,
                  bankCardNo,
                  bankName,
                  payChannelCode,
                  cloudOpenStatus,
                  wsyDisplaySwitch,
                  dlgSignStatue,
                  receiveType,
                  email,
                  hkpaySignStatue
                } = res.data.sessionInfo;

                const { appName, appLogoUrl, bannerUrl, registerPageUrl } =
                  res.data.appInfPo;

                this.$store.commit(
                  'SET_CURRENTMODE',
                  payMarketMode == 0 ? 1 : payMarketMode
                );
                this.$store.commit('SET_USERINFO', {
                  agentCode,
                  realName,
                  accountName,
                  agentLevel,
                  mobile,
                  payMarketMode,
                  companyName,
                  appAuthStatus,
                  payTaxType,
                  taxationRegisterStatus,
                  idCardNo,
                  superAgentCode,
                  showCreditDiscountOpenConf,
                  showSecTransOpenConf,
                  memberLevel,
                  isMobileVerify,
                  loginName,
                  bankCardNo,
                  bankName,
                  payChannelCode,
                  cloudOpenStatus,
                  wsyDisplaySwitch,
                  dlgSignStatue,
                  hkpaySignStatue,
                  receiveType,
                  email
                });

                this.$store.commit('SET_APPINFO', {
                  appName,
                  appLogoUrl,
                  bannerUrl,
                  registerPageUrl
                });

                if (this.remember) {
                  const { loginUserNo, loginPwd } = this.user;
                  if (
                    !this.accounts.some(
                      (a) =>
                        a.loginUserNo == loginUserNo && a.loginPwd == loginPwd
                    )
                  ) {
                    this.accounts.push({ loginUserNo, loginPwd });
                  }
                }
                uni.setStorageSync('accounts', this.accounts);

                uni.setStorageSync('last_login_account', this.user.loginUserNo);

                uni.setStorageSync('agreement', true);
                //    个人认证状态 => appAuthStatus  0 未认证
                if (appAuthStatus == 0) {
                  this.$Router.push({
                    name: 'Authentification',
                    params: { token: res.data.sessionId }
                  });
                } else {
                  this.$store.commit('SET_TOKEN', res.data.sessionId);
                  this.$Router.pushTab({ name: 'Home' });
                  getChannel()
                    .then((res) => {
                      res.code == '00' &&
                        this.$store.commit('SET_PAYORG_CODES', res.data);
                    })
                    .catch((err) => console.log(err));
                }
              }
            });
          }
        });
      },
      register() {
        this.$Router.push({ name: 'Register' });
      },
      toWebview(type) {
        const url = type
          ? 'https://openappyc.shtcloud.com/privacyRulesSht'
          : 'https://openappyc.shtcloud.com/userServRulesSht';
        this.$Router.push({
          name: 'WebView',
          params: { title: type ? '隐私规则' : '服务协议', url }
        });
      },
     async checkIosIsPass() {
        const os = uni.getSystemInfoSync();
        if (os.platform === 'ios') {
          plus.runtime.getProperty(plus.runtime.appid, async (inf) => {
            const { data } = await loginSwitch({ version: inf.version });
            if (data.loginSwitch === 1) {
              this.$store.commit('SET_ISIOSCHECKPASS', 0);
              const hideTabbarIndexs = [1, 2];
              hideTabbarIndexs.forEach((index) => {
                uni.setTabBarItem({
                  index,
                  visible: false
                });
              });
            } else {
              this.$store.commit('SET_ISIOSCHECKPASS', 1);
              const hideTabbarIndexs = [1, 2];
              hideTabbarIndexs.forEach((index) => {
                uni.setTabBarItem({
                  index,
                  visible: true
                });
              });
            }

            return Promise.resolve();
          });
        } else {
          const hideTabbarIndexs = [1, 2];
          hideTabbarIndexs.forEach((index) => {
            uni.setTabBarItem({
              index,
              visible: true
            });
          });
          this.$store.commit('SET_ISIOSCHECKPASS', 1);
          return Promise.resolve();
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  #login {
    min-height: 100%;
    background-color: #fff;
    header {
      padding: 30rpx;
      > text {
        font-weight: 500;
        font-size: 50rpx;
      }
    }
    main {
      padding: 0 30rpx 30rpx;
      .rember-psw {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24rpx;
        > text {
          padding: 0 3rpx;
          font-size: 24rpx;
          color: #000;
          text {
            color: #004ea9;
            margin-left: 20rpx;
          }
        }
      }
      /deep/ .u-form-item__body {
        flex-direction: column !important;
      }
      /deep/ .u-input__right-icon__item {
        .u-icon {
          text {
            font-size: 40rpx !important;
          }
        }
      }
      /deep/ .u-checkbox__label {
        font-size: 26rpx;
      }

      /deep/ .select-down {
        .u-icon {
          position: relative;
          &::after {
            content: '';
            position: absolute;
            top: -20rpx;
            bottom: -20rpx;
            left: -20rpx;
            right: -20rpx;
            background-color: transparent;
          }
        }
      }
    }
    .logo {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: fixed;
      bottom: 30rpx;
      width: 100%;
      color: #999;
      font-size: 22rpx;
      image {
        width: 100rpx;
        height: 100rpx;
        margin-bottom: 20rpx;
      }
    }
    .proup-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1px solid #f3f5f7;
      background-color: #fff;
      > text {
        font-weight: 500;
      }
    }
    /deep/ .u-cell {
      padding: 18rpx 32rpx;
    }

    .agreement {
      display: flex;
      align-items: center;
      justify-content: center;
      /deep/ .u-checkbox__label {
        display: none;
      }
      /deep/ .u-checkbox {
        display: block;
      }
      > p {
        margin-left: 16rpx;
        > span {
          color: #023875;
        }
      }
    }
  }
</style>
